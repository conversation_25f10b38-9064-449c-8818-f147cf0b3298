<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quotes', function (Blueprint $table) {
            $table->id();
            $table->string('quote_number')->unique();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('company_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('service_id')->constrained()->onDelete('cascade');
            $table->enum('type', ['instant', 'custom'])->default('instant');
            $table->enum('status', ['draft', 'pending', 'approved', 'rejected', 'expired'])->default('draft');

            // Origin and destination
            $table->json('origin')->nullable(); // Address, coordinates, etc.
            $table->json('destination')->nullable();

            // Shipment details
            $table->json('cargo_details')->nullable(); // Weight, dimensions, type, etc.
            $table->decimal('total_weight', 10, 2)->nullable();
            $table->string('weight_unit', 10)->default('kg');
            $table->json('dimensions')->nullable();

            // Pricing
            $table->decimal('base_price', 10, 2)->nullable();
            $table->decimal('additional_fees', 10, 2)->default(0);
            $table->decimal('total_price', 10, 2)->nullable();
            $table->string('currency', 3)->default('USD');

            // Validity and notes
            $table->date('valid_until')->nullable();
            $table->text('notes')->nullable();
            $table->text('admin_notes')->nullable();
            $table->json('metadata')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quotes');
    }
};
