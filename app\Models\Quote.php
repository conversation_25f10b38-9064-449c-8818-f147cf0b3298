<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Quote extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'quote_number',
        'user_id',
        'company_id',
        'service_id',
        'type',
        'status',
        'origin',
        'destination',
        'cargo_details',
        'total_weight',
        'weight_unit',
        'dimensions',
        'base_price',
        'additional_fees',
        'total_price',
        'currency',
        'valid_until',
        'notes',
        'admin_notes',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected function casts(): array
    {
        return [
            'origin' => 'array',
            'destination' => 'array',
            'cargo_details' => 'array',
            'dimensions' => 'array',
            'metadata' => 'array',
            'base_price' => 'decimal:2',
            'additional_fees' => 'decimal:2',
            'total_price' => 'decimal:2',
            'total_weight' => 'decimal:2',
            'valid_until' => 'date',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($quote) {
            if (empty($quote->quote_number)) {
                $quote->quote_number = 'QT-' . strtoupper(Str::random(8));
            }
        });
    }

    /**
     * Get the user that owns the quote.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the company that owns the quote.
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Get the service for the quote.
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    /**
     * Get the shipments created from this quote.
     */
    public function shipments(): HasMany
    {
        return $this->hasMany(Shipment::class);
    }

    /**
     * Check if quote is expired.
     */
    public function isExpired(): bool
    {
        return $this->valid_until && $this->valid_until->isPast();
    }

    /**
     * Check if quote is approved.
     */
    public function isApproved(): bool
    {
        return $this->status === 'approved';
    }

    /**
     * Scope a query to only include active quotes.
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['draft', 'pending', 'approved'])
                    ->where(function ($q) {
                        $q->whereNull('valid_until')
                          ->orWhere('valid_until', '>=', now());
                    });
    }

    /**
     * Calculate total price including additional fees.
     */
    public function calculateTotalPrice(): float
    {
        return ($this->base_price ?? 0) + ($this->additional_fees ?? 0);
    }
}
