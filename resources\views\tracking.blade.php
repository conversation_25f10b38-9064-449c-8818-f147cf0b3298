<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>Shipment Tracking - {{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased bg-background text-foreground">
    <div class="min-h-screen flex flex-col">
        <!-- Navigation -->
        <header class="sticky top-0 z-40 w-full border-b bg-background">
            <div class="container flex h-16 items-center justify-between py-4">
                <div class="flex items-center gap-2 md:gap-10">
                    <!-- Logo -->
                    <a href="{{ url('/') }}" class="flex items-center gap-2">
                        <img src="{{ asset('images/logo_dcf.png') }}" alt="DCF Logistics" class="h-8 w-auto">
                    </a>
                    <nav class="hidden md:flex">
                        <ul class="flex items-center space-x-6">
                            <li><a href="{{ url('/') }}" class="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">Home</a></li>
                            <li><a href="/services" class="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">Services</a></li>
                            <li><a href="/tracking" class="text-sm font-medium text-foreground">Tracking</a></li>
                            <li><a href="/quote" class="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">Get Quote</a></li>
                        </ul>
                    </nav>
                </div>

                <!-- Auth Links -->
                <div class="flex items-center gap-4">
                    @if (Route::has('login'))
                        @auth
                            <a href="{{ url('/dashboard') }}" class="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">Dashboard</a>
                        @else
                            <a href="{{ route('login') }}" class="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">Login</a>
                            @if (Route::has('register'))
                                <a href="{{ route('register') }}" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">Get Started</a>
                            @endif
                        @endauth
                    @endif
                </div>
            </div>
        </header>

        <main class="flex-1">
            <!-- Hero Section -->
            <section class="bg-gradient-to-r from-gray-900 to-gray-800 text-white py-16">
                <div class="container px-4 md:px-6 mx-auto">
                    <div class="max-w-3xl mx-auto text-center">
                        <h1 class="text-3xl md:text-4xl font-bold mb-6">Track Your Shipment</h1>
                        <p class="text-xl text-gray-300 mb-8">
                            Enter your tracking number to get real-time updates on your shipment's status and location.
                        </p>
                        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 md:p-8">
                            <form action="{{ route('tracking.results') }}" method="GET" class="w-full">
                                <div class="flex flex-col sm:flex-row gap-3">
                                    <div class="flex-1">
                                        <input
                                            type="text"
                                            name="trackingNumber"
                                            placeholder="Enter your tracking number"
                                            class="flex h-12 w-full rounded-md border border-input bg-white text-gray-900 px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                            required
                                        />
                                    </div>
                                    <button type="submit" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-12 px-6">
                                        <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                        Track Shipment
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </section>

            <!-- How Tracking Works Section -->
            <section class="py-16 bg-gray-50">
                <div class="container px-4 md:px-6 mx-auto">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl font-bold tracking-tight text-gray-900">How Tracking Works</h2>
                        <p class="mt-4 text-lg text-gray-600 max-w-2xl mx-auto">
                            Our advanced tracking system provides you with real-time updates on your shipment's journey.
                        </p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div class="bg-white p-6 rounded-lg shadow-sm text-center">
                            <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="h-8 w-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-semibold mb-3">Enter Tracking Number</h3>
                            <p class="text-gray-600">
                                Input your unique tracking number provided when your shipment was processed.
                            </p>
                        </div>

                        <div class="bg-white p-6 rounded-lg shadow-sm text-center">
                            <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="h-8 w-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-semibold mb-3">View Status</h3>
                            <p class="text-gray-600">
                                Get detailed information about your shipment's current status and location.
                            </p>
                        </div>

                        <div class="bg-white p-6 rounded-lg shadow-sm text-center">
                            <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="h-8 w-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-semibold mb-3">Track Journey</h3>
                            <p class="text-gray-600">
                                Follow your shipment's complete journey with timestamps for each milestone.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- FAQ Section -->
            <section class="py-16 bg-white">
                <div class="container px-4 md:px-6 mx-auto">
                    <div class="max-w-3xl mx-auto">
                        <div class="text-center mb-12">
                            <h2 class="text-3xl font-bold tracking-tight text-gray-900">Tracking FAQs</h2>
                            <p class="mt-4 text-lg text-gray-600">Common questions about our shipment tracking system</p>
                        </div>

                        <div class="space-y-6">
                            <div class="bg-gray-50 p-6 rounded-lg">
                                <h3 class="text-xl font-semibold mb-2">Where can I find my tracking number?</h3>
                                <p class="text-gray-600">
                                    Your tracking number is provided in the shipping confirmation email you received when your shipment
                                    was processed. It can also be found on your shipping receipt or invoice.
                                </p>
                            </div>

                            <div class="bg-gray-50 p-6 rounded-lg">
                                <h3 class="text-xl font-semibold mb-2">How often is tracking information updated?</h3>
                                <p class="text-gray-600">
                                    Tracking information is updated in real-time as your shipment moves through our logistics network.
                                    Updates typically occur when your shipment arrives at or departs from a facility, or when it's out
                                    for delivery.
                                </p>
                            </div>

                            <div class="bg-gray-50 p-6 rounded-lg">
                                <h3 class="text-xl font-semibold mb-2">What if my tracking information isn't updating?</h3>
                                <p class="text-gray-600">
                                    If your tracking information hasn't updated in 24-48 hours, it may be due to processing delays or
                                    transit between facilities. If you're concerned, please contact our customer service team for
                                    assistance.
                                </p>
                            </div>

                            <div class="bg-gray-50 p-6 rounded-lg">
                                <h3 class="text-xl font-semibold mb-2">Can I track multiple shipments at once?</h3>
                                <p class="text-gray-600">
                                    Currently, our tracking system allows you to track one shipment at a time. For businesses with
                                    multiple shipments, we offer a comprehensive dashboard through our client portal.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Contact Section -->
            <section class="py-16 bg-gray-50">
                <div class="container px-4 md:px-6 mx-auto">
                    <div class="max-w-3xl mx-auto text-center">
                        <h2 class="text-3xl font-bold tracking-tight text-gray-900 mb-4">Need Additional Help?</h2>
                        <p class="text-lg text-gray-600 mb-8">
                            Our customer service team is ready to assist you with any questions about your shipment.
                        </p>
                        <div class="flex flex-col sm:flex-row gap-4 justify-center">
                            <a href="/contact" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-11 px-8">
                                Contact Support
                            </a>
                            <a href="/services" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-11 px-8">
                                Explore Our Services
                            </a>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="border-t bg-background">
            <div class="container py-12 md:py-16">
                <div class="grid gap-8 lg:grid-cols-5">
                    <div class="lg:col-span-2">
                        <a href="{{ url('/') }}" class="flex items-center gap-2 mb-4">
                            <img src="{{ asset('images/logo_dcf.png') }}" alt="DCF Logistics" class="h-8 w-auto">
                        </a>
                        <p class="text-muted-foreground mb-4 max-w-xs">Your trusted partner for comprehensive logistics solutions worldwide.</p>
                    </div>
                    <div>
                        <h3 class="font-semibold mb-4">Services</h3>
                        <ul class="space-y-2 text-sm">
                            <li><a href="/services/air-freight" class="text-muted-foreground hover:text-foreground transition-colors">Air Freight</a></li>
                            <li><a href="/services/customs-clearance" class="text-muted-foreground hover:text-foreground transition-colors">Customs Clearance</a></li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-semibold mb-4">Company</h3>
                        <ul class="space-y-2 text-sm">
                            <li><a href="/about" class="text-muted-foreground hover:text-foreground transition-colors">About Us</a></li>
                            <li><a href="/contact" class="text-muted-foreground hover:text-foreground transition-colors">Contact</a></li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-semibold mb-4">Support</h3>
                        <ul class="space-y-2 text-sm">
                            <li><a href="/tracking" class="text-muted-foreground hover:text-foreground transition-colors">Track Shipment</a></li>
                            <li><a href="/quote" class="text-muted-foreground hover:text-foreground transition-colors">Get Quote</a></li>
                        </ul>
                    </div>
                </div>
                <div class="border-t mt-12 pt-8 text-center">
                    <p class="text-xs text-muted-foreground">© {{ date('Y') }} DCF Logistics. All rights reserved.</p>
                </div>
            </div>
        </footer>
    </div>
</body>
</html>
