<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Service;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class ServiceController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'permission:view services'])->only(['index', 'show']);
        $this->middleware(['auth', 'permission:create services'])->only(['create', 'store']);
        $this->middleware(['auth', 'permission:edit services'])->only(['edit', 'update']);
        $this->middleware(['auth', 'permission:delete services'])->only(['destroy']);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Service::withCount(['quotes', 'shipments']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by status
        if ($request->filled('is_active')) {
            $query->where('is_active', $request->is_active === '1');
        }

        $services = $query->paginate(15)->withQueryString();

        return view('admin.services.index', compact('services'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.services.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:50|unique:services',
            'description' => 'nullable|string',
            'type' => 'required|in:air,ground,customs,warehousing,other',
            'base_price' => 'required|numeric|min:0',
            'currency' => 'required|string|size:3',
            'estimated_days' => 'nullable|integer|min:1',
            'is_active' => 'boolean',
            'pricing_rules' => 'nullable|json',
            'requirements' => 'nullable|json',
            'metadata' => 'nullable|json',
        ]);

        $data = $request->all();
        
        // Parse JSON fields
        if ($request->filled('pricing_rules')) {
            $data['pricing_rules'] = json_decode($request->pricing_rules, true);
        }
        
        if ($request->filled('requirements')) {
            $data['requirements'] = json_decode($request->requirements, true);
        }
        
        if ($request->filled('metadata')) {
            $data['metadata'] = json_decode($request->metadata, true);
        }

        Service::create($data);

        return redirect()->route('admin.services.index')
            ->with('success', 'Service created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Service $service)
    {
        $service->load(['quotes.user', 'shipments.user']);
        
        return view('admin.services.show', compact('service'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Service $service)
    {
        return view('admin.services.edit', compact('service'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Service $service)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => ['required', 'string', 'max:50', Rule::unique('services')->ignore($service->id)],
            'description' => 'nullable|string',
            'type' => 'required|in:air,ground,customs,warehousing,other',
            'base_price' => 'required|numeric|min:0',
            'currency' => 'required|string|size:3',
            'estimated_days' => 'nullable|integer|min:1',
            'is_active' => 'boolean',
            'pricing_rules' => 'nullable|json',
            'requirements' => 'nullable|json',
            'metadata' => 'nullable|json',
        ]);

        $data = $request->all();
        
        // Parse JSON fields
        if ($request->filled('pricing_rules')) {
            $data['pricing_rules'] = json_decode($request->pricing_rules, true);
        }
        
        if ($request->filled('requirements')) {
            $data['requirements'] = json_decode($request->requirements, true);
        }
        
        if ($request->filled('metadata')) {
            $data['metadata'] = json_decode($request->metadata, true);
        }

        $service->update($data);

        return redirect()->route('admin.services.index')
            ->with('success', 'Service updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Service $service)
    {
        // Check if service has active quotes or shipments
        if ($service->quotes()->whereIn('status', ['pending', 'approved'])->count() > 0) {
            return redirect()->route('admin.services.index')
                ->with('error', 'Cannot delete service with active quotes.');
        }

        if ($service->shipments()->active()->count() > 0) {
            return redirect()->route('admin.services.index')
                ->with('error', 'Cannot delete service with active shipments.');
        }

        $service->delete();

        return redirect()->route('admin.services.index')
            ->with('success', 'Service deleted successfully.');
    }

    /**
     * Toggle service active status.
     */
    public function toggleStatus(Service $service)
    {
        $service->update(['is_active' => !$service->is_active]);

        $status = $service->is_active ? 'activated' : 'deactivated';
        
        return redirect()->back()
            ->with('success', "Service {$status} successfully.");
    }

    /**
     * Duplicate a service.
     */
    public function duplicate(Service $service)
    {
        $newService = $service->replicate();
        $newService->name = $service->name . ' (Copy)';
        $newService->code = $service->code . '_COPY_' . time();
        $newService->is_active = false;
        $newService->save();

        return redirect()->route('admin.services.edit', $newService)
            ->with('success', 'Service duplicated successfully. Please review and update the details.');
    }
}
