<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\Quote;
use App\Models\Shipment;
use App\Models\User;
use Illuminate\Http\Request;

class AdminController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:admin,super_admin']);
    }

    /**
     * Display the admin dashboard.
     */
    public function dashboard()
    {
        $stats = [
            'total_users' => User::count(),
            'total_companies' => Company::count(),
            'total_quotes' => Quote::count(),
            'total_shipments' => Shipment::count(),
            'active_shipments' => Shipment::active()->count(),
            'pending_quotes' => Quote::where('status', 'pending')->count(),
        ];

        $recentQuotes = Quote::with(['user', 'company', 'service'])
            ->latest()
            ->take(5)
            ->get();

        $recentShipments = Shipment::with(['user', 'company', 'service'])
            ->latest()
            ->take(5)
            ->get();

        return view('admin.dashboard', compact('stats', 'recentQuotes', 'recentShipments'));
    }

    /**
     * Display system analytics.
     */
    public function analytics()
    {
        // Monthly quote statistics
        $monthlyQuotes = Quote::selectRaw('MONTH(created_at) as month, COUNT(*) as count')
            ->whereYear('created_at', date('Y'))
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        // Monthly shipment statistics
        $monthlyShipments = Shipment::selectRaw('MONTH(created_at) as month, COUNT(*) as count')
            ->whereYear('created_at', date('Y'))
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        // Quote status distribution
        $quoteStatusStats = Quote::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get();

        // Shipment status distribution
        $shipmentStatusStats = Shipment::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->get();

        // Top companies by quote volume
        $topCompaniesByQuotes = Company::withCount('quotes')
            ->orderBy('quotes_count', 'desc')
            ->take(10)
            ->get();

        // Revenue statistics (if pricing data is available)
        $monthlyRevenue = Quote::selectRaw('MONTH(created_at) as month, SUM(total_price) as revenue')
            ->where('status', 'approved')
            ->whereYear('created_at', date('Y'))
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        return view('admin.analytics', compact(
            'monthlyQuotes',
            'monthlyShipments',
            'quoteStatusStats',
            'shipmentStatusStats',
            'topCompaniesByQuotes',
            'monthlyRevenue'
        ));
    }

    /**
     * Display system settings.
     */
    public function settings()
    {
        return view('admin.settings');
    }

    /**
     * Update system settings.
     */
    public function updateSettings(Request $request)
    {
        $request->validate([
            'site_name' => 'required|string|max:255',
            'site_email' => 'required|email',
            'default_currency' => 'required|string|size:3',
            'timezone' => 'required|string',
        ]);

        // In a real application, you would store these in a settings table or config
        // For now, we'll just return success

        return redirect()->route('admin.settings')
            ->with('success', 'Settings updated successfully.');
    }
}
