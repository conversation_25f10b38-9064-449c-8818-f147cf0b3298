<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }} - Dashboard</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased bg-background text-foreground">
    <div class="flex min-h-screen flex-col">
        <!-- Header -->
        <header class="sticky top-0 z-40 w-full border-b bg-background">
            <div class="container flex h-16 items-center justify-between py-4">
                <div class="flex items-center gap-2 md:gap-10">
                    <!-- Logo -->
                    <a href="{{ url('/') }}" class="flex items-center gap-2">
                        <img src="{{ asset('images/logo_dcf.png') }}" alt="DCF Logistics" class="h-8 w-auto">
                    </a>
                    <nav class="hidden md:flex">
                        <ul class="flex items-center space-x-6">
                            <li><a href="{{ url('/') }}" class="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">Home</a></li>
                            <li><a href="/services" class="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">Services</a></li>
                            <li><a href="/tracking" class="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">Tracking</a></li>
                        </ul>
                    </nav>
                </div>

                <!-- User Menu -->
                <div class="flex items-center gap-4">
                    <div class="flex items-center gap-2">
                        <span class="text-sm font-medium">{{ Auth::user()->name }}</span>
                        <form method="POST" action="{{ route('logout') }}" class="inline">
                            @csrf
                            <button type="submit" class="text-sm text-muted-foreground hover:text-foreground transition-colors">Logout</button>
                        </form>
                    </div>
                </div>
            </div>
        </header>

        <div class="flex-1 flex">
            <!-- Sidebar -->
            <aside class="hidden md:flex w-64 flex-col border-r bg-gray-50/50 min-h-screen">
                <div class="flex flex-col h-full justify-between">
                    <div class="space-y-6">
                        <div class="px-3 py-6">
                            <h2 class="mb-4 px-3 text-lg font-semibold">Client Portal</h2>
                            <nav class="flex flex-col space-y-1">
                                <a href="{{ route('dashboard') }}" class="flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium {{ request()->routeIs('dashboard') ? 'bg-gray-100 text-gray-900' : 'text-gray-500 hover:bg-gray-100 hover:text-gray-900' }}">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h2a2 2 0 012 2v6H8V5z"></path>
                                    </svg>
                                    Dashboard
                                </a>
                                <a href="/account/shipments" class="flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-900">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                    </svg>
                                    Shipments
                                </a>
                                <a href="/tracking" class="flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-900">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Tracking
                                </a>
                                <a href="/account/documents" class="flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-900">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    Documents
                                </a>
                                <a href="/account/inventory" class="flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-900">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                    Inventory
                                </a>
                                <a href="/account/settings" class="flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-900">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    Settings
                                </a>
                                <a href="/account/support" class="flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium text-gray-500 hover:bg-gray-100 hover:text-gray-900">
                                    <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Support
                                </a>
                            </nav>
                        </div>
                    </div>
                    <div class="p-3">
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" class="w-full flex items-center justify-start gap-2 rounded-md border border-input bg-background px-3 py-2 text-sm font-medium text-gray-500 hover:bg-accent hover:text-accent-foreground transition-colors">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                                Logout
                            </button>
                        </form>
                    </div>
                </div>
            </aside>

            <!-- Main Content -->
            <main class="flex-1 p-6">
                <div class="space-y-6">
                    <!-- Header -->
                    <div>
                        <h1 class="text-3xl font-bold tracking-tight">Dashboard</h1>
                        <p class="text-muted-foreground">Welcome back to your DCF Logistics client portal</p>
                    </div>

                    <!-- Stats Cards -->
                    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                        <!-- Active Shipments -->
                        <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                            <div class="flex flex-row items-center justify-between space-y-0 p-6 pb-2">
                                <h3 class="text-sm font-medium">Active Shipments</h3>
                                <svg class="h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                </svg>
                            </div>
                            <div class="p-6 pt-0">
                                <div class="text-2xl font-bold">4</div>
                                <p class="text-xs text-muted-foreground">Currently in transit</p>
                            </div>
                        </div>

                        <!-- Pending Shipments -->
                        <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                            <div class="flex flex-row items-center justify-between space-y-0 p-6 pb-2">
                                <h3 class="text-sm font-medium">Pending Shipments</h3>
                                <svg class="h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div class="p-6 pt-0">
                                <div class="text-2xl font-bold">2</div>
                                <p class="text-xs text-muted-foreground">Awaiting processing</p>
                            </div>
                        </div>

                        <!-- Documents -->
                        <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                            <div class="flex flex-row items-center justify-between space-y-0 p-6 pb-2">
                                <h3 class="text-sm font-medium">Documents</h3>
                                <svg class="h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            </div>
                            <div class="p-6 pt-0">
                                <div class="text-2xl font-bold">12</div>
                                <p class="text-xs text-muted-foreground">Available for download</p>
                            </div>
                        </div>

                        <!-- Notifications -->
                        <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                            <div class="flex flex-row items-center justify-between space-y-0 p-6 pb-2">
                                <h3 class="text-sm font-medium">Notifications</h3>
                                <svg class="h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4.021 9.869A9.966 9.966 0 014 9c0-5.523 4.477-10 10-10s10 4.477 10 10c0 .34-.017.677-.05 1.011M4.021 9.869c-.001-.023-.001-.046-.001-.069 0-.34.017-.677.05-1.011M4.021 9.869A9.966 9.966 0 004 10c0 5.523 4.477 10 10 10 .34 0 .677-.017 1.011-.05"></path>
                                </svg>
                            </div>
                            <div class="p-6 pt-0">
                                <div class="text-2xl font-bold">3</div>
                                <p class="text-xs text-muted-foreground">Unread alerts</p>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Shipments and Upcoming Shipments -->
                    <div class="grid gap-4 md:grid-cols-2">
                        <!-- Recent Shipments -->
                        <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                            <div class="p-6 pb-4">
                                <h3 class="text-lg font-semibold">Recent Shipments</h3>
                                <p class="text-sm text-muted-foreground">Overview of your latest shipments</p>
                            </div>
                            <div class="p-6 pt-0">
                                <div class="space-y-4">
                                    <div class="border-b pb-4">
                                        <div class="flex justify-between items-center mb-1">
                                            <div class="font-medium">DCF-10234</div>
                                            <div class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">In Transit</div>
                                        </div>
                                        <div class="text-sm text-muted-foreground mb-1">Banjul to Lagos</div>
                                        <div class="flex justify-between text-xs">
                                            <span>Estimated delivery: {{ date('M d, Y', strtotime('+3 days')) }}</span>
                                            <a href="/tracking/DCF-10234" class="text-primary hover:underline">Track</a>
                                        </div>
                                    </div>

                                    <div class="border-b pb-4">
                                        <div class="flex justify-between items-center mb-1">
                                            <div class="font-medium">DCF-10233</div>
                                            <div class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Processing</div>
                                        </div>
                                        <div class="text-sm text-muted-foreground mb-1">Accra to Dakar</div>
                                        <div class="flex justify-between text-xs">
                                            <span>Estimated delivery: {{ date('M d, Y', strtotime('+6 days')) }}</span>
                                            <a href="/tracking/DCF-10233" class="text-primary hover:underline">Track</a>
                                        </div>
                                    </div>

                                    <div class="border-b pb-4">
                                        <div class="flex justify-between items-center mb-1">
                                            <div class="font-medium">DCF-10232</div>
                                            <div class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">Delivered</div>
                                        </div>
                                        <div class="text-sm text-muted-foreground mb-1">Dakar to Banjul</div>
                                        <div class="flex justify-between text-xs">
                                            <span>Delivered: {{ date('M d, Y', strtotime('-3 days')) }}</span>
                                            <a href="/tracking/DCF-10232" class="text-primary hover:underline">Details</a>
                                        </div>
                                    </div>

                                    <div>
                                        <div class="flex justify-between items-center mb-1">
                                            <div class="font-medium">DCF-10231</div>
                                            <div class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">Delivered</div>
                                        </div>
                                        <div class="text-sm text-muted-foreground mb-1">Lagos to Accra</div>
                                        <div class="flex justify-between text-xs">
                                            <span>Delivered: {{ date('M d, Y', strtotime('-6 days')) }}</span>
                                            <a href="/tracking/DCF-10231" class="text-primary hover:underline">Details</a>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4 text-center">
                                    <a href="/account/shipments" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3">
                                        View All Shipments
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Upcoming Shipments -->
                        <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                            <div class="p-6 pb-4">
                                <h3 class="text-lg font-semibold">Upcoming Shipments</h3>
                                <p class="text-sm text-muted-foreground">Scheduled shipments for the next 30 days</p>
                            </div>
                            <div class="p-6 pt-0">
                                <div class="space-y-4">
                                    <div class="flex items-start gap-4 border-b pb-4">
                                        <div class="bg-primary/10 p-2 rounded-md">
                                            <svg class="h-5 w-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10a2 2 0 002 2h4a2 2 0 002-2V11m-6 0a2 2 0 002-2h4a2 2 0 002 2m-6 0h6m-6 4h6m-6 4h6"></path>
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <div class="font-medium">Air Freight: Banjul to Accra</div>
                                            <div class="text-sm text-muted-foreground">Scheduled for {{ date('M d, Y', strtotime('+2 days')) }}</div>
                                            <div class="text-xs text-muted-foreground mt-1">Reference: DCF-10235</div>
                                        </div>
                                    </div>

                                    <div class="flex items-start gap-4 border-b pb-4">
                                        <div class="bg-primary/10 p-2 rounded-md">
                                            <svg class="h-5 w-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10a2 2 0 002 2h4a2 2 0 002-2V11m-6 0a2 2 0 002-2h4a2 2 0 002 2m-6 0h6m-6 4h6m-6 4h6"></path>
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <div class="font-medium">Ocean Freight: Lagos to Dakar</div>
                                            <div class="text-sm text-muted-foreground">Scheduled for {{ date('M d, Y', strtotime('+8 days')) }}</div>
                                            <div class="text-xs text-muted-foreground mt-1">Reference: DCF-10236</div>
                                        </div>
                                    </div>

                                    <div class="flex items-start gap-4">
                                        <div class="bg-primary/10 p-2 rounded-md">
                                            <svg class="h-5 w-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10a2 2 0 002 2h4a2 2 0 002-2V11m-6 0a2 2 0 002-2h4a2 2 0 002 2m-6 0h6m-6 4h6m-6 4h6"></path>
                                            </svg>
                                        </div>
                                        <div class="flex-1">
                                            <div class="font-medium">Road Transport: Accra to Banjul</div>
                                            <div class="text-sm text-muted-foreground">Scheduled for {{ date('M d, Y', strtotime('+14 days')) }}</div>
                                            <div class="text-xs text-muted-foreground mt-1">Reference: DCF-10237</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4 text-center">
                                    <a href="/quote" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3">
                                        Book New Shipment
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bottom Section -->
                    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                        <!-- Shipping Activity Chart -->
                        <div class="col-span-2 rounded-lg border bg-card text-card-foreground shadow-sm">
                            <div class="p-6 pb-4">
                                <h3 class="text-lg font-semibold">Shipping Activity</h3>
                                <p class="text-sm text-muted-foreground">Your shipping volume over the past 6 months</p>
                            </div>
                            <div class="p-6 pt-0">
                                <div class="h-[200px] flex items-center justify-center border rounded-md bg-muted/20">
                                    <svg class="h-8 w-8 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                                    </svg>
                                    <span class="ml-2 text-muted-foreground">Shipping activity chart</span>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Documents -->
                        <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                            <div class="p-6 pb-4">
                                <h3 class="text-lg font-semibold">Recent Documents</h3>
                                <p class="text-sm text-muted-foreground">Latest shipping documents</p>
                            </div>
                            <div class="p-6 pt-0">
                                <div class="space-y-4">
                                    <div class="flex items-center gap-2">
                                        <svg class="h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        <div class="text-sm font-medium">Invoice - DCF-10234</div>
                                        <div class="ml-auto">
                                            <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-6 w-6">
                                                <svg class="h-4 w-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4m14-4l-5 5-5-5m5 5V3"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>

                                    <div class="flex items-center gap-2">
                                        <svg class="h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        <div class="text-sm font-medium">Bill of Lading - DCF-10233</div>
                                        <div class="ml-auto">
                                            <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-6 w-6">
                                                <svg class="h-4 w-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4m14-4l-5 5-5-5m5 5V3"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>

                                    <div class="flex items-center gap-2">
                                        <svg class="h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        <div class="text-sm font-medium">Customs Declaration - DCF-10234</div>
                                        <div class="ml-auto">
                                            <button class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-6 w-6">
                                                <svg class="h-4 w-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4m14-4l-5 5-5-5m5 5V3"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-4 text-center">
                                    <a href="/account/documents" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3">
                                        View All Documents
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
</body>
</html>
