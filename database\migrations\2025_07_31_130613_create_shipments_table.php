<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shipments', function (Blueprint $table) {
            $table->id();
            $table->string('tracking_number')->unique();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('company_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('service_id')->constrained()->onDelete('cascade');
            $table->foreignId('quote_id')->nullable()->constrained()->onDelete('set null');

            $table->enum('status', [
                'pending', 'confirmed', 'picked_up', 'in_transit',
                'customs_clearance', 'out_for_delivery', 'delivered',
                'exception', 'cancelled'
            ])->default('pending');

            // Origin and destination
            $table->json('origin_address');
            $table->json('destination_address');

            // Shipment details
            $table->json('cargo_details');
            $table->decimal('total_weight', 10, 2);
            $table->string('weight_unit', 10)->default('kg');
            $table->json('dimensions')->nullable();

            // Pricing
            $table->decimal('total_cost', 10, 2);
            $table->string('currency', 3)->default('USD');

            // Dates
            $table->datetime('pickup_date')->nullable();
            $table->datetime('estimated_delivery')->nullable();
            $table->datetime('actual_delivery')->nullable();

            // Additional info
            $table->text('special_instructions')->nullable();
            $table->json('documents')->nullable(); // Uploaded document references
            $table->json('metadata')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shipments');
    }
};
