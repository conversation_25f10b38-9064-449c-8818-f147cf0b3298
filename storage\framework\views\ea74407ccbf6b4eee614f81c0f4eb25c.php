<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title>Request a Quote - <?php echo e(config('app.name', 'Laravel')); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Scripts -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
</head>
<body class="font-sans antialiased bg-background text-foreground">
    <div class="min-h-screen flex flex-col">
        <!-- Navigation -->
        <header class="sticky top-0 z-40 w-full border-b bg-background">
            <div class="container flex h-16 items-center justify-between py-4">
                <div class="flex items-center gap-2 md:gap-10">
                    <!-- Logo -->
                    <a href="<?php echo e(url('/')); ?>" class="flex items-center gap-2">
                        <img src="<?php echo e(asset('images/logo_dcf.png')); ?>" alt="DCF Logistics" class="h-8 w-auto">
                    </a>
                    <nav class="hidden md:flex">
                        <ul class="flex items-center space-x-6">
                            <li><a href="<?php echo e(url('/')); ?>" class="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">Home</a></li>
                            <li><a href="/services" class="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">Services</a></li>
                            <li><a href="/tracking" class="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">Tracking</a></li>
                            <li><a href="/quote" class="text-sm font-medium text-foreground">Get Quote</a></li>
                        </ul>
                    </nav>
                </div>

                <!-- Auth Links -->
                <div class="flex items-center gap-4">
                    <?php if(Route::has('login')): ?>
                        <?php if(auth()->guard()->check()): ?>
                            <a href="<?php echo e(url('/dashboard')); ?>" class="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">Dashboard</a>
                        <?php else: ?>
                            <a href="<?php echo e(route('login')); ?>" class="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">Login</a>
                            <?php if(Route::has('register')): ?>
                                <a href="<?php echo e(route('register')); ?>" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">Get Started</a>
                            <?php endif; ?>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </header>

        <main class="flex-1">
            <!-- Hero Section -->
            <section class="bg-gradient-to-r from-gray-900 to-gray-800 text-white py-16">
                <div class="container px-4 md:px-6 mx-auto">
                    <div class="max-w-3xl mx-auto text-center">
                        <h1 class="text-3xl md:text-4xl font-bold mb-6">Request a Quote</h1>
                        <p class="text-xl text-gray-300 mb-8">
                            Get a customized quote for your logistics needs. Fill out the form below and our team will provide you
                            with competitive pricing tailored to your requirements.
                        </p>
                    </div>
                </div>
            </section>

            <!-- Quote Form Section -->
            <section class="py-16 bg-gray-50">
                <div class="container px-4 md:px-6 mx-auto">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        <div class="lg:col-span-2">
                            <!-- Quote Request Form -->
                            <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                                <div class="p-6 pb-4">
                                    <h3 class="text-lg font-semibold">Request a Quote</h3>
                                    <p class="text-sm text-muted-foreground">
                                        Fill out the form below with your shipping details to receive a customized quote.
                                    </p>
                                </div>
                                <div class="p-6 pt-0">
                                    <form class="space-y-6" method="POST" action="/quote">
                                        <?php echo csrf_field(); ?>
                                        
                                        <!-- Contact Information -->
                                        <div class="space-y-4">
                                            <h3 class="text-lg font-medium">Contact Information</h3>
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <div class="space-y-2">
                                                    <label for="fullName" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Full Name *</label>
                                                    <input id="fullName" name="fullName" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" required />
                                                </div>
                                                <div class="space-y-2">
                                                    <label for="email" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Email Address *</label>
                                                    <input id="email" name="email" type="email" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" required />
                                                </div>
                                                <div class="space-y-2">
                                                    <label for="phone" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Phone Number *</label>
                                                    <input id="phone" name="phone" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" required />
                                                </div>
                                                <div class="space-y-2">
                                                    <label for="company" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Company Name</label>
                                                    <input id="company" name="company" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" />
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Service Details -->
                                        <div class="space-y-4">
                                            <h3 class="text-lg font-medium">Service Details</h3>
                                            <div class="space-y-2">
                                                <label for="serviceType" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Service Type *</label>
                                                <select id="serviceType" name="serviceType" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" required>
                                                    <option value="">Select service type</option>
                                                    <option value="freight-forwarding">Freight Forwarding</option>
                                                    <option value="customs-brokerage">Customs Brokerage</option>
                                                    <option value="warehousing">Warehousing & Distribution</option>
                                                    <option value="air-freight">Air Freight</option>
                                                    <option value="ocean-freight">Ocean Freight</option>
                                                    <option value="road-transport">Road Transportation</option>
                                                    <option value="cross-border">Cross Border Logistics</option>
                                                    <option value="other">Other</option>
                                                </select>
                                            </div>

                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <div class="space-y-2">
                                                    <label for="origin" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Origin Location *</label>
                                                    <input id="origin" name="origin" placeholder="City, Country" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" required />
                                                </div>
                                                <div class="space-y-2">
                                                    <label for="destination" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Destination Location *</label>
                                                    <input id="destination" name="destination" placeholder="City, Country" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" required />
                                                </div>
                                            </div>

                                            <div class="space-y-2">
                                                <label for="shipmentDate" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Estimated Shipment Date</label>
                                                <input id="shipmentDate" name="shipmentDate" type="date" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" />
                                            </div>
                                        </div>

                                        <!-- Cargo Information -->
                                        <div class="space-y-4">
                                            <h3 class="text-lg font-medium">Cargo Information</h3>
                                            <div class="space-y-2">
                                                <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Cargo Type *</label>
                                                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                                                    <div class="flex items-center space-x-2">
                                                        <input type="radio" id="general" name="cargoType" value="general" class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary" required />
                                                        <label for="general" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">General Cargo</label>
                                                    </div>
                                                    <div class="flex items-center space-x-2">
                                                        <input type="radio" id="hazardous" name="cargoType" value="hazardous" class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary" />
                                                        <label for="hazardous" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Hazardous Materials</label>
                                                    </div>
                                                    <div class="flex items-center space-x-2">
                                                        <input type="radio" id="perishable" name="cargoType" value="perishable" class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary" />
                                                        <label for="perishable" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Perishable Goods</label>
                                                    </div>
                                                    <div class="flex items-center space-x-2">
                                                        <input type="radio" id="oversized" name="cargoType" value="oversized" class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary" />
                                                        <label for="oversized" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Oversized Cargo</label>
                                                    </div>
                                                    <div class="flex items-center space-x-2">
                                                        <input type="radio" id="valuable" name="cargoType" value="valuable" class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary" />
                                                        <label for="valuable" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">High-Value Items</label>
                                                    </div>
                                                    <div class="flex items-center space-x-2">
                                                        <input type="radio" id="other-cargo" name="cargoType" value="other" class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary" />
                                                        <label for="other-cargo" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Other</label>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="space-y-2">
                                                <label for="cargoDetails" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Cargo Description *</label>
                                                <textarea id="cargoDetails" name="cargoDetails" placeholder="Please describe your cargo in detail" class="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" required></textarea>
                                            </div>

                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <div class="space-y-2">
                                                    <label for="dimensions" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Dimensions</label>
                                                    <input id="dimensions" name="dimensions" placeholder="Length x Width x Height (cm)" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" />
                                                </div>
                                                <div class="space-y-2">
                                                    <label for="weight" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Weight</label>
                                                    <input id="weight" name="weight" placeholder="Total weight (kg)" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50" />
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Additional Services -->
                                        <div class="space-y-4">
                                            <h3 class="text-lg font-medium">Additional Services</h3>
                                            <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
                                                <div class="flex items-center space-x-2">
                                                    <input type="checkbox" id="insurance" name="additionalServices[]" value="insurance" class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary" />
                                                    <label for="insurance" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Cargo Insurance</label>
                                                </div>
                                                <div class="flex items-center space-x-2">
                                                    <input type="checkbox" id="customs" name="additionalServices[]" value="customs" class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary" />
                                                    <label for="customs" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Customs Clearance</label>
                                                </div>
                                                <div class="flex items-center space-x-2">
                                                    <input type="checkbox" id="packaging" name="additionalServices[]" value="packaging" class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary" />
                                                    <label for="packaging" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Packaging Services</label>
                                                </div>
                                                <div class="flex items-center space-x-2">
                                                    <input type="checkbox" id="warehousing" name="additionalServices[]" value="warehousing" class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary" />
                                                    <label for="warehousing" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Warehousing</label>
                                                </div>
                                                <div class="flex items-center space-x-2">
                                                    <input type="checkbox" id="door-to-door" name="additionalServices[]" value="door-to-door" class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary" />
                                                    <label for="door-to-door" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Door-to-Door Delivery</label>
                                                </div>
                                                <div class="flex items-center space-x-2">
                                                    <input type="checkbox" id="tracking" name="additionalServices[]" value="tracking" class="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary" />
                                                    <label for="tracking" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Advanced Tracking</label>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="space-y-2">
                                            <label for="specialRequirements" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">Special Requirements or Instructions</label>
                                            <textarea id="specialRequirements" name="specialRequirements" placeholder="Any special handling instructions or requirements" class="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"></textarea>
                                        </div>

                                        <button type="submit" class="w-full inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">
                                            Submit Quote Request
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Sidebar -->
                        <div class="space-y-6">
                            <!-- Why Choose Us -->
                            <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                                <div class="p-6 pb-4">
                                    <h3 class="text-lg font-semibold">Why Choose Us</h3>
                                    <p class="text-sm text-muted-foreground">Benefits of our logistics services</p>
                                </div>
                                <div class="p-6 pt-0 space-y-4">
                                    <div class="flex items-start gap-3">
                                        <svg class="h-5 w-5 text-primary flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <div>
                                            <h3 class="font-semibold text-gray-900">Competitive Pricing</h3>
                                            <p class="text-sm text-gray-600">We offer cost-effective solutions without compromising on quality</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start gap-3">
                                        <svg class="h-5 w-5 text-primary flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <div>
                                            <h3 class="font-semibold text-gray-900">Global Network</h3>
                                            <p class="text-sm text-gray-600">Extensive network of partners for seamless logistics worldwide</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start gap-3">
                                        <svg class="h-5 w-5 text-primary flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <div>
                                            <h3 class="font-semibold text-gray-900">Customized Solutions</h3>
                                            <p class="text-sm text-gray-600">Tailored logistics services to meet your specific requirements</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start gap-3">
                                        <svg class="h-5 w-5 text-primary flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <div>
                                            <h3 class="font-semibold text-gray-900">Dedicated Support</h3>
                                            <p class="text-sm text-gray-600">Personalized customer service throughout your shipment journey</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Information -->
                            <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                                <div class="p-6 pb-4">
                                    <h3 class="text-lg font-semibold">Contact Information</h3>
                                    <p class="text-sm text-muted-foreground">Reach out to us directly</p>
                                </div>
                                <div class="p-6 pt-0 space-y-4">
                                    <div class="flex items-center gap-3">
                                        <svg class="h-5 w-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                        </svg>
                                        <span class="text-gray-600">+220 123 456 789</span>
                                    </div>
                                    <div class="flex items-center gap-3">
                                        <svg class="h-5 w-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                        </svg>
                                        <span class="text-gray-600"><EMAIL></span>
                                    </div>
                                    <div class="flex items-center gap-3">
                                        <svg class="h-5 w-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <div class="text-gray-600">
                                            <div>Monday - Friday: 8:00 AM - 6:00 PM</div>
                                            <div>Saturday: 9:00 AM - 2:00 PM</div>
                                        </div>
                                    </div>
                                    <div class="flex items-center gap-3">
                                        <svg class="h-5 w-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        <span class="text-gray-600">For urgent quotes, please call our dedicated quote hotline</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Quote Process Section -->
            <section class="py-16 bg-white">
                <div class="container px-4 md:px-6 mx-auto">
                    <div class="max-w-3xl mx-auto text-center">
                        <h2 class="text-3xl font-bold tracking-tight text-gray-900 mb-6">Our Quote Process</h2>
                        <p class="text-lg text-gray-600 mb-12">We strive to provide accurate and competitive quotes as quickly as possible</p>

                        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                            <div class="text-center">
                                <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <span class="text-primary font-bold text-xl">1</span>
                                </div>
                                <h3 class="text-xl font-semibold mb-2">Submit Request</h3>
                                <p class="text-gray-600">Fill out our quote request form with details about your shipment requirements</p>
                            </div>
                            <div class="text-center">
                                <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <span class="text-primary font-bold text-xl">2</span>
                                </div>
                                <h3 class="text-xl font-semibold mb-2">Receive Quote</h3>
                                <p class="text-gray-600">Our team will analyze your requirements and provide a detailed quote within 24 hours</p>
                            </div>
                            <div class="text-center">
                                <div class="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <span class="text-primary font-bold text-xl">3</span>
                                </div>
                                <h3 class="text-xl font-semibold mb-2">Confirm & Book</h3>
                                <p class="text-gray-600">Review your quote, confirm the details, and book your logistics services with us</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="border-t bg-background">
            <div class="container py-12 md:py-16">
                <div class="grid gap-8 lg:grid-cols-5">
                    <div class="lg:col-span-2">
                        <a href="<?php echo e(url('/')); ?>" class="flex items-center gap-2 mb-4">
                            <img src="<?php echo e(asset('images/logo_dcf.png')); ?>" alt="DCF Logistics" class="h-8 w-auto">
                        </a>
                        <p class="text-muted-foreground mb-4 max-w-xs">Your trusted partner for comprehensive logistics solutions worldwide.</p>
                    </div>
                    <div>
                        <h3 class="font-semibold mb-4">Services</h3>
                        <ul class="space-y-2 text-sm">
                            <li><a href="/services/air-freight" class="text-muted-foreground hover:text-foreground transition-colors">Air Freight</a></li>
                            <li><a href="/services/customs-clearance" class="text-muted-foreground hover:text-foreground transition-colors">Customs Clearance</a></li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-semibold mb-4">Company</h3>
                        <ul class="space-y-2 text-sm">
                            <li><a href="/about" class="text-muted-foreground hover:text-foreground transition-colors">About Us</a></li>
                            <li><a href="/contact" class="text-muted-foreground hover:text-foreground transition-colors">Contact</a></li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-semibold mb-4">Support</h3>
                        <ul class="space-y-2 text-sm">
                            <li><a href="/tracking" class="text-muted-foreground hover:text-foreground transition-colors">Track Shipment</a></li>
                            <li><a href="/quote" class="text-muted-foreground hover:text-foreground transition-colors">Get Quote</a></li>
                        </ul>
                    </div>
                </div>
                <div class="border-t mt-12 pt-8 text-center">
                    <p class="text-xs text-muted-foreground">© <?php echo e(date('Y')); ?> DCF Logistics. All rights reserved.</p>
                </div>
            </div>
        </footer>
    </div>
</body>
</html>
<?php /**PATH C:\DCF AGENCY WEB\dcf-logistics-laravel\resources\views/quote.blade.php ENDPATH**/ ?>