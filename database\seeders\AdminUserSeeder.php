<?php

namespace Database\Seeders;

use App\Models\Company;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create DCF Logistics company
        $dcfCompany = Company::create([
            'name' => 'DCF Logistics',
            'legal_name' => 'DCF Logistics LLC',
            'email' => '<EMAIL>',
            'phone' => '******-0123',
            'address' => '123 Logistics Way',
            'city' => 'Miami',
            'state' => 'FL',
            'postal_code' => '33101',
            'country' => 'US',
            'website' => 'https://dcflogistics.com',
            'status' => 'active',
        ]);

        // Create super admin user
        $superAdmin = User::create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'company_id' => $dcfCompany->id,
            'job_title' => 'System Administrator',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);
        $superAdmin->assignRole('super_admin');

        // Create admin user
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'company_id' => $dcfCompany->id,
            'job_title' => 'Administrator',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);
        $admin->assignRole('admin');

        // Create demo company
        $demoCompany = Company::create([
            'name' => 'Demo Company Inc',
            'legal_name' => 'Demo Company Incorporated',
            'email' => '<EMAIL>',
            'phone' => '******-0456',
            'address' => '456 Business Ave',
            'city' => 'New York',
            'state' => 'NY',
            'postal_code' => '10001',
            'country' => 'US',
            'status' => 'active',
        ]);

        // Create company admin user
        $companyAdmin = User::create([
            'name' => 'Company Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'company_id' => $demoCompany->id,
            'job_title' => 'Logistics Manager',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);
        $companyAdmin->assignRole('company_admin');

        // Create customer user
        $customer = User::create([
            'name' => 'Demo Customer',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'company_id' => $demoCompany->id,
            'job_title' => 'Shipping Coordinator',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);
        $customer->assignRole('customer');
    }
}
