<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tracking_events', function (Blueprint $table) {
            $table->id();
            $table->foreignId('shipment_id')->constrained()->onDelete('cascade');
            $table->string('event_type'); // picked_up, in_transit, delivered, etc.
            $table->string('status'); // Current shipment status after this event
            $table->text('description');
            $table->json('location')->nullable(); // Address, coordinates, facility info
            $table->datetime('event_datetime');
            $table->string('source')->default('system'); // system, carrier, manual
            $table->json('metadata')->nullable(); // Additional event data
            $table->timestamps();

            $table->index(['shipment_id', 'event_datetime']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tracking_events');
    }
};
