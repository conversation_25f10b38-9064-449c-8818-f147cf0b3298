<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ChatConversation extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'title',
        'status',
        'context',
        'last_activity_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected function casts(): array
    {
        return [
            'context' => 'array',
            'last_activity_at' => 'datetime',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($conversation) {
            $conversation->last_activity_at = now();
        });
    }

    /**
     * Get the user that owns the conversation.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the messages for the conversation.
     */
    public function messages(): HasMany
    {
        return $this->hasMany(ChatMessage::class, 'conversation_id')->orderBy('created_at');
    }

    /**
     * Get the latest message.
     */
    public function latestMessage()
    {
        return $this->hasOne(ChatMessage::class, 'conversation_id')->latestOfMany();
    }

    /**
     * Scope a query to only include active conversations.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to order by last activity.
     */
    public function scopeRecentActivity($query)
    {
        return $query->orderBy('last_activity_at', 'desc');
    }

    /**
     * Update last activity timestamp.
     */
    public function updateActivity(): void
    {
        $this->update(['last_activity_at' => now()]);
    }

    /**
     * Add a message to the conversation.
     */
    public function addMessage(string $message, string $senderType = 'user', ?int $senderId = null, array $metadata = []): ChatMessage
    {
        $chatMessage = $this->messages()->create([
            'sender_type' => $senderType,
            'sender_id' => $senderId,
            'message' => $message,
            'metadata' => $metadata,
        ]);

        $this->updateActivity();

        return $chatMessage;
    }

    /**
     * Generate a title for the conversation based on the first message.
     */
    public function generateTitle(): void
    {
        if ($this->title || !$this->messages()->exists()) {
            return;
        }

        $firstMessage = $this->messages()->first();
        $title = substr($firstMessage->message, 0, 50);

        if (strlen($firstMessage->message) > 50) {
            $title .= '...';
        }

        $this->update(['title' => $title]);
    }
}
