<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Service extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'code',
        'description',
        'type',
        'pricing_rules',
        'base_price',
        'currency',
        'estimated_days',
        'is_active',
        'requirements',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected function casts(): array
    {
        return [
            'pricing_rules' => 'array',
            'requirements' => 'array',
            'metadata' => 'array',
            'is_active' => 'boolean',
            'base_price' => 'decimal:2',
        ];
    }

    /**
     * Get the quotes for the service.
     */
    public function quotes(): HasMany
    {
        return $this->hasMany(Quote::class);
    }

    /**
     * Get the shipments for the service.
     */
    public function shipments(): HasMany
    {
        return $this->hasMany(Shipment::class);
    }

    /**
     * Scope a query to only include active services.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to filter by service type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Calculate price based on cargo details and pricing rules.
     */
    public function calculatePrice(array $cargoDetails): float
    {
        $basePrice = $this->base_price ?? 0;

        // Apply pricing rules if they exist
        if ($this->pricing_rules) {
            // This would contain complex pricing logic
            // For now, return base price
            return $basePrice;
        }

        return $basePrice;
    }

    /**
     * Check if service is available for given route.
     */
    public function isAvailableForRoute(array $origin, array $destination): bool
    {
        // This would contain route availability logic
        // For now, return true if service is active
        return $this->is_active;
    }
}
