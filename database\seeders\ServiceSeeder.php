<?php

namespace Database\Seeders;

use App\Models\Service;
use Illuminate\Database\Seeder;

class ServiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $services = [
            [
                'name' => 'Air Freight Express',
                'code' => 'AIR_EXPRESS',
                'description' => 'Fast air freight service for urgent shipments worldwide',
                'type' => 'air',
                'base_price' => 25.00,
                'currency' => 'USD',
                'estimated_days' => 3,
                'is_active' => true,
                'pricing_rules' => [
                    'weight_tiers' => [
                        ['min' => 0, 'max' => 10, 'rate' => 25.00],
                        ['min' => 10, 'max' => 50, 'rate' => 20.00],
                        ['min' => 50, 'max' => 100, 'rate' => 15.00],
                        ['min' => 100, 'max' => null, 'rate' => 12.00],
                    ],
                    'dimensional_weight_factor' => 167,
                ],
                'requirements' => [
                    'documents' => ['commercial_invoice', 'packing_list'],
                    'restrictions' => ['no_hazardous_materials', 'max_weight_500kg'],
                ],
            ],
            [
                'name' => 'Air Freight Standard',
                'code' => 'AIR_STANDARD',
                'description' => 'Standard air freight service with competitive rates',
                'type' => 'air',
                'base_price' => 15.00,
                'currency' => 'USD',
                'estimated_days' => 7,
                'is_active' => true,
                'pricing_rules' => [
                    'weight_tiers' => [
                        ['min' => 0, 'max' => 10, 'rate' => 15.00],
                        ['min' => 10, 'max' => 50, 'rate' => 12.00],
                        ['min' => 50, 'max' => 100, 'rate' => 10.00],
                        ['min' => 100, 'max' => null, 'rate' => 8.00],
                    ],
                    'dimensional_weight_factor' => 167,
                ],
                'requirements' => [
                    'documents' => ['commercial_invoice', 'packing_list'],
                    'restrictions' => ['no_hazardous_materials'],
                ],
            ],
            [
                'name' => 'Ground Freight Express',
                'code' => 'GROUND_EXPRESS',
                'description' => 'Fast ground transportation for domestic shipments',
                'type' => 'ground',
                'base_price' => 8.00,
                'currency' => 'USD',
                'estimated_days' => 2,
                'is_active' => true,
                'pricing_rules' => [
                    'weight_tiers' => [
                        ['min' => 0, 'max' => 50, 'rate' => 8.00],
                        ['min' => 50, 'max' => 200, 'rate' => 6.00],
                        ['min' => 200, 'max' => 500, 'rate' => 4.00],
                        ['min' => 500, 'max' => null, 'rate' => 3.00],
                    ],
                    'distance_factor' => 0.001,
                ],
                'requirements' => [
                    'documents' => ['bill_of_lading'],
                    'restrictions' => ['domestic_only'],
                ],
            ],
            [
                'name' => 'Ground Freight Standard',
                'code' => 'GROUND_STANDARD',
                'description' => 'Economical ground transportation service',
                'type' => 'ground',
                'base_price' => 5.00,
                'currency' => 'USD',
                'estimated_days' => 5,
                'is_active' => true,
                'pricing_rules' => [
                    'weight_tiers' => [
                        ['min' => 0, 'max' => 50, 'rate' => 5.00],
                        ['min' => 50, 'max' => 200, 'rate' => 4.00],
                        ['min' => 200, 'max' => 500, 'rate' => 3.00],
                        ['min' => 500, 'max' => null, 'rate' => 2.50],
                    ],
                    'distance_factor' => 0.0008,
                ],
                'requirements' => [
                    'documents' => ['bill_of_lading'],
                    'restrictions' => ['domestic_only'],
                ],
            ],
            [
                'name' => 'Customs Clearance',
                'code' => 'CUSTOMS_CLEAR',
                'description' => 'Professional customs clearance and documentation service',
                'type' => 'customs',
                'base_price' => 75.00,
                'currency' => 'USD',
                'estimated_days' => 1,
                'is_active' => true,
                'pricing_rules' => [
                    'flat_rate' => true,
                    'additional_fees' => [
                        'inspection_fee' => 50.00,
                        'storage_per_day' => 25.00,
                    ],
                ],
                'requirements' => [
                    'documents' => [
                        'commercial_invoice',
                        'packing_list',
                        'certificate_of_origin',
                        'import_license',
                    ],
                ],
            ],
            [
                'name' => 'Warehousing Service',
                'code' => 'WAREHOUSE',
                'description' => 'Secure warehousing and inventory management',
                'type' => 'warehousing',
                'base_price' => 2.00,
                'currency' => 'USD',
                'estimated_days' => null,
                'is_active' => true,
                'pricing_rules' => [
                    'per_pallet_per_day' => 2.00,
                    'handling_fee' => 15.00,
                    'minimum_days' => 7,
                ],
                'requirements' => [
                    'documents' => ['inventory_list'],
                    'restrictions' => ['no_perishables', 'no_hazardous_materials'],
                ],
            ],
        ];

        foreach ($services as $serviceData) {
            Service::create($serviceData);
        }
    }
}
