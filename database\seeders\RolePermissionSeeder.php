<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // User management
            'view users',
            'create users',
            'edit users',
            'delete users',

            // Company management
            'view companies',
            'create companies',
            'edit companies',
            'delete companies',

            // Service management
            'view services',
            'create services',
            'edit services',
            'delete services',

            // Quote management
            'view quotes',
            'create quotes',
            'edit quotes',
            'delete quotes',
            'approve quotes',

            // Shipment management
            'view shipments',
            'create shipments',
            'edit shipments',
            'delete shipments',
            'track shipments',

            // Chat system
            'use chat',
            'view chat history',
            'moderate chat',

            // Admin functions
            'access admin panel',
            'view system logs',
            'manage system settings',
            'view analytics',

            // Company-specific permissions
            'view own company data',
            'edit own company data',
            'manage company users',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create roles and assign permissions

        // Super Admin - has all permissions
        $superAdmin = Role::create(['name' => 'super_admin']);
        $superAdmin->givePermissionTo(Permission::all());

        // Admin - has most permissions except super admin functions
        $admin = Role::create(['name' => 'admin']);
        $admin->givePermissionTo([
            'view users', 'create users', 'edit users', 'delete users',
            'view companies', 'create companies', 'edit companies', 'delete companies',
            'view services', 'create services', 'edit services', 'delete services',
            'view quotes', 'create quotes', 'edit quotes', 'delete quotes', 'approve quotes',
            'view shipments', 'create shipments', 'edit shipments', 'delete shipments', 'track shipments',
            'use chat', 'view chat history', 'moderate chat',
            'access admin panel', 'view system logs', 'view analytics',
        ]);

        // Company Admin - can manage their company and users
        $companyAdmin = Role::create(['name' => 'company_admin']);
        $companyAdmin->givePermissionTo([
            'view own company data', 'edit own company data', 'manage company users',
            'view quotes', 'create quotes', 'edit quotes',
            'view shipments', 'create shipments', 'edit shipments', 'track shipments',
            'use chat', 'view chat history',
            'view services',
        ]);

        // Customer - basic user permissions
        $customer = Role::create(['name' => 'customer']);
        $customer->givePermissionTo([
            'view quotes', 'create quotes',
            'view shipments', 'track shipments',
            'use chat', 'view chat history',
            'view services',
            'view own company data',
        ]);

        // Guest - very limited permissions
        $guest = Role::create(['name' => 'guest']);
        $guest->givePermissionTo([
            'view services',
            'create quotes',
        ]);
    }
}
