<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('services', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('code')->unique();
            $table->text('description')->nullable();
            $table->enum('type', ['air', 'ground', 'customs', 'warehousing', 'other']);
            $table->json('pricing_rules')->nullable();
            $table->decimal('base_price', 10, 2)->nullable();
            $table->string('currency', 3)->default('USD');
            $table->integer('estimated_days')->nullable();
            $table->boolean('is_active')->default(true);
            $table->json('requirements')->nullable(); // Document requirements, restrictions, etc.
            $table->json('metadata')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('services');
    }
};
