<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>Tracking Results - {{ config('app.name', 'Laravel') }}</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased bg-background text-foreground">
    <div class="min-h-screen flex flex-col">
        <!-- Navigation -->
        <header class="sticky top-0 z-40 w-full border-b bg-background">
            <div class="container flex h-16 items-center justify-between py-4">
                <div class="flex items-center gap-2 md:gap-10">
                    <!-- Logo -->
                    <a href="{{ url('/') }}" class="flex items-center gap-2">
                        <img src="{{ asset('images/logo_dcf.png') }}" alt="DCF Logistics" class="h-8 w-auto">
                    </a>
                    <nav class="hidden md:flex">
                        <ul class="flex items-center space-x-6">
                            <li><a href="{{ url('/') }}" class="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">Home</a></li>
                            <li><a href="/services" class="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">Services</a></li>
                            <li><a href="/tracking" class="text-sm font-medium text-foreground">Tracking</a></li>
                            <li><a href="/quote" class="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">Get Quote</a></li>
                        </ul>
                    </nav>
                </div>

                <!-- Auth Links -->
                <div class="flex items-center gap-4">
                    @if (Route::has('login'))
                        @auth
                            <a href="{{ url('/dashboard') }}" class="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">Dashboard</a>
                        @else
                            <a href="{{ route('login') }}" class="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors">Login</a>
                            @if (Route::has('register'))
                                <a href="{{ route('register') }}" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">Get Started</a>
                            @endif
                        @endauth
                    @endif
                </div>
            </div>
        </header>

        <main class="flex-1">
            <!-- Header Section -->
            <section class="bg-gray-50 py-8">
                <div class="container px-4 md:px-6 mx-auto">
                    <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                        <div>
                            <h1 class="text-2xl md:text-3xl font-bold">Tracking Results</h1>
                            <p class="text-muted-foreground">Tracking Number: <span class="font-medium">{{ $trackingNumber }}</span></p>
                        </div>
                        <a href="{{ route('tracking') }}" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2">
                            Track Another Shipment
                        </a>
                    </div>
                </div>
            </section>

            <!-- Tracking Results Section -->
            <section class="py-8">
                <div class="container px-4 md:px-6 mx-auto">
                    <div class="grid gap-8 lg:grid-cols-3">
                        <!-- Main Tracking Info -->
                        <div class="lg:col-span-2 space-y-6">
                            <!-- Shipment Status Card -->
                            <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                                <div class="p-6">
                                    <div class="flex items-center justify-between mb-4">
                                        <h2 class="text-xl font-semibold">Shipment Status</h2>
                                        <div class="bg-green-100 text-green-800 text-sm px-3 py-1 rounded-full font-medium">
                                            In Transit
                                        </div>
                                    </div>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <p class="text-sm text-muted-foreground">Origin</p>
                                            <p class="font-medium">Banjul, Gambia</p>
                                        </div>
                                        <div>
                                            <p class="text-sm text-muted-foreground">Destination</p>
                                            <p class="font-medium">Lagos, Nigeria</p>
                                        </div>
                                        <div>
                                            <p class="text-sm text-muted-foreground">Service Type</p>
                                            <p class="font-medium">Air Freight Express</p>
                                        </div>
                                        <div>
                                            <p class="text-sm text-muted-foreground">Estimated Delivery</p>
                                            <p class="font-medium">{{ date('M d, Y', strtotime('+2 days')) }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Tracking Timeline -->
                            <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                                <div class="p-6">
                                    <h2 class="text-xl font-semibold mb-6">Tracking Timeline</h2>
                                    <div class="space-y-6">
                                        <!-- Current Status -->
                                        <div class="flex gap-4">
                                            <div class="flex flex-col items-center">
                                                <div class="w-4 h-4 bg-primary rounded-full"></div>
                                                <div class="w-0.5 h-16 bg-primary"></div>
                                            </div>
                                            <div class="flex-1 pb-8">
                                                <div class="flex items-center justify-between mb-1">
                                                    <h3 class="font-semibold text-primary">In Transit to Destination</h3>
                                                    <span class="text-sm text-muted-foreground">{{ date('M d, Y H:i', strtotime('-2 hours')) }}</span>
                                                </div>
                                                <p class="text-sm text-muted-foreground">Your shipment is currently in transit and on its way to the destination facility.</p>
                                                <p class="text-sm text-muted-foreground mt-1">Location: En route to Lagos, Nigeria</p>
                                            </div>
                                        </div>

                                        <!-- Previous Status -->
                                        <div class="flex gap-4">
                                            <div class="flex flex-col items-center">
                                                <div class="w-4 h-4 bg-gray-400 rounded-full"></div>
                                                <div class="w-0.5 h-16 bg-gray-300"></div>
                                            </div>
                                            <div class="flex-1 pb-8">
                                                <div class="flex items-center justify-between mb-1">
                                                    <h3 class="font-semibold">Departed from Origin Facility</h3>
                                                    <span class="text-sm text-muted-foreground">{{ date('M d, Y H:i', strtotime('-8 hours')) }}</span>
                                                </div>
                                                <p class="text-sm text-muted-foreground">Shipment has departed from the origin facility and is en route to destination.</p>
                                                <p class="text-sm text-muted-foreground mt-1">Location: Banjul International Airport, Gambia</p>
                                            </div>
                                        </div>

                                        <!-- Earlier Status -->
                                        <div class="flex gap-4">
                                            <div class="flex flex-col items-center">
                                                <div class="w-4 h-4 bg-gray-400 rounded-full"></div>
                                                <div class="w-0.5 h-16 bg-gray-300"></div>
                                            </div>
                                            <div class="flex-1 pb-8">
                                                <div class="flex items-center justify-between mb-1">
                                                    <h3 class="font-semibold">Processed at Origin Facility</h3>
                                                    <span class="text-sm text-muted-foreground">{{ date('M d, Y H:i', strtotime('-1 day')) }}</span>
                                                </div>
                                                <p class="text-sm text-muted-foreground">Shipment has been processed and prepared for departure at the origin facility.</p>
                                                <p class="text-sm text-muted-foreground mt-1">Location: DCF Logistics Banjul Hub</p>
                                            </div>
                                        </div>

                                        <!-- Initial Status -->
                                        <div class="flex gap-4">
                                            <div class="flex flex-col items-center">
                                                <div class="w-4 h-4 bg-gray-400 rounded-full"></div>
                                            </div>
                                            <div class="flex-1">
                                                <div class="flex items-center justify-between mb-1">
                                                    <h3 class="font-semibold">Shipment Received</h3>
                                                    <span class="text-sm text-muted-foreground">{{ date('M d, Y H:i', strtotime('-2 days')) }}</span>
                                                </div>
                                                <p class="text-sm text-muted-foreground">Your shipment has been received and is being processed at our facility.</p>
                                                <p class="text-sm text-muted-foreground mt-1">Location: DCF Logistics Banjul Hub</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Sidebar -->
                        <div class="space-y-6">
                            <!-- Shipment Details -->
                            <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                                <div class="p-6">
                                    <h3 class="text-lg font-semibold mb-4">Shipment Details</h3>
                                    <div class="space-y-3">
                                        <div>
                                            <p class="text-sm text-muted-foreground">Weight</p>
                                            <p class="font-medium">25.5 kg</p>
                                        </div>
                                        <div>
                                            <p class="text-sm text-muted-foreground">Dimensions</p>
                                            <p class="font-medium">60 x 40 x 30 cm</p>
                                        </div>
                                        <div>
                                            <p class="text-sm text-muted-foreground">Service</p>
                                            <p class="font-medium">Air Freight Express</p>
                                        </div>
                                        <div>
                                            <p class="text-sm text-muted-foreground">Reference</p>
                                            <p class="font-medium">{{ $trackingNumber }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Contact Support -->
                            <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                                <div class="p-6">
                                    <h3 class="text-lg font-semibold mb-4">Need Help?</h3>
                                    <p class="text-sm text-muted-foreground mb-4">
                                        If you have questions about your shipment, our customer service team is here to help.
                                    </p>
                                    <div class="space-y-3">
                                        <div class="flex items-center gap-2">
                                            <svg class="h-4 w-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                            </svg>
                                            <span class="text-sm">+220 123 456 789</span>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <svg class="h-4 w-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                            </svg>
                                            <span class="text-sm"><EMAIL></span>
                                        </div>
                                    </div>
                                    <a href="/contact" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-3 w-full mt-4">
                                        Contact Support
                                    </a>
                                </div>
                            </div>

                            <!-- Delivery Options -->
                            <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                                <div class="p-6">
                                    <h3 class="text-lg font-semibold mb-4">Delivery Options</h3>
                                    <div class="space-y-3">
                                        <div class="flex items-center gap-2">
                                            <svg class="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            <span class="text-sm">Door-to-door delivery</span>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <svg class="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            <span class="text-sm">Signature required</span>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <svg class="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            <span class="text-sm">SMS notifications</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="border-t bg-background">
            <div class="container py-12 md:py-16">
                <div class="grid gap-8 lg:grid-cols-5">
                    <div class="lg:col-span-2">
                        <a href="{{ url('/') }}" class="flex items-center gap-2 mb-4">
                            <img src="{{ asset('images/logo_dcf.png') }}" alt="DCF Logistics" class="h-8 w-auto">
                        </a>
                        <p class="text-muted-foreground mb-4 max-w-xs">Your trusted partner for comprehensive logistics solutions worldwide.</p>
                    </div>
                    <div>
                        <h3 class="font-semibold mb-4">Services</h3>
                        <ul class="space-y-2 text-sm">
                            <li><a href="/services/air-freight" class="text-muted-foreground hover:text-foreground transition-colors">Air Freight</a></li>
                            <li><a href="/services/customs-clearance" class="text-muted-foreground hover:text-foreground transition-colors">Customs Clearance</a></li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-semibold mb-4">Company</h3>
                        <ul class="space-y-2 text-sm">
                            <li><a href="/about" class="text-muted-foreground hover:text-foreground transition-colors">About Us</a></li>
                            <li><a href="/contact" class="text-muted-foreground hover:text-foreground transition-colors">Contact</a></li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-semibold mb-4">Support</h3>
                        <ul class="space-y-2 text-sm">
                            <li><a href="/tracking" class="text-muted-foreground hover:text-foreground transition-colors">Track Shipment</a></li>
                            <li><a href="/quote" class="text-muted-foreground hover:text-foreground transition-colors">Get Quote</a></li>
                        </ul>
                    </div>
                </div>
                <div class="border-t mt-12 pt-8 text-center">
                    <p class="text-xs text-muted-foreground">© {{ date('Y') }} DCF Logistics. All rights reserved.</p>
                </div>
            </div>
        </footer>
    </div>
</body>
</html>
