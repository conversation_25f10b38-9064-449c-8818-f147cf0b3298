<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ChatMessage extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'conversation_id',
        'sender_type',
        'sender_id',
        'message',
        'metadata',
        'is_edited',
        'edited_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected function casts(): array
    {
        return [
            'metadata' => 'array',
            'is_edited' => 'boolean',
            'edited_at' => 'datetime',
        ];
    }

    /**
     * Get the conversation that owns the message.
     */
    public function conversation(): BelongsTo
    {
        return $this->belongsTo(ChatConversation::class, 'conversation_id');
    }

    /**
     * Get the sender (user) of the message.
     */
    public function sender(): BelongsTo
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    /**
     * Check if message is from user.
     */
    public function isFromUser(): bool
    {
        return $this->sender_type === 'user';
    }

    /**
     * Check if message is from AI.
     */
    public function isFromAI(): bool
    {
        return $this->sender_type === 'ai';
    }

    /**
     * Check if message is from admin.
     */
    public function isFromAdmin(): bool
    {
        return $this->sender_type === 'admin';
    }

    /**
     * Scope a query to only include user messages.
     */
    public function scopeFromUser($query)
    {
        return $query->where('sender_type', 'user');
    }

    /**
     * Scope a query to only include AI messages.
     */
    public function scopeFromAI($query)
    {
        return $query->where('sender_type', 'ai');
    }

    /**
     * Scope a query to order by creation time.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('created_at');
    }

    /**
     * Edit the message.
     */
    public function editMessage(string $newMessage): void
    {
        $this->update([
            'message' => $newMessage,
            'is_edited' => true,
            'edited_at' => now(),
        ]);
    }

    /**
     * Get the sender name for display.
     */
    public function getSenderNameAttribute(): string
    {
        switch ($this->sender_type) {
            case 'user':
                return $this->sender ? $this->sender->name : 'User';
            case 'ai':
                return 'AI Assistant';
            case 'admin':
                return $this->sender ? $this->sender->name . ' (Admin)' : 'Admin';
            default:
                return 'Unknown';
        }
    }
}
