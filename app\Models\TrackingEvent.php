<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TrackingEvent extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'shipment_id',
        'event_type',
        'status',
        'description',
        'location',
        'event_datetime',
        'source',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected function casts(): array
    {
        return [
            'location' => 'array',
            'metadata' => 'array',
            'event_datetime' => 'datetime',
        ];
    }

    /**
     * Get the shipment that owns the tracking event.
     */
    public function shipment(): BelongsTo
    {
        return $this->belongsTo(Shipment::class);
    }

    /**
     * Scope a query to order by event datetime.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('event_datetime', 'desc');
    }

    /**
     * Scope a query to filter by event type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('event_type', $type);
    }

    /**
     * Get formatted location string.
     */
    public function getFormattedLocationAttribute(): string
    {
        if (!$this->location) {
            return 'Unknown Location';
        }

        $parts = [];

        if (isset($this->location['facility'])) {
            $parts[] = $this->location['facility'];
        }

        if (isset($this->location['city'])) {
            $parts[] = $this->location['city'];
        }

        if (isset($this->location['state'])) {
            $parts[] = $this->location['state'];
        }

        if (isset($this->location['country'])) {
            $parts[] = $this->location['country'];
        }

        return implode(', ', $parts) ?: 'Unknown Location';
    }

    /**
     * Check if this is a delivery event.
     */
    public function isDeliveryEvent(): bool
    {
        return $this->event_type === 'delivered';
    }

    /**
     * Check if this is an exception event.
     */
    public function isExceptionEvent(): bool
    {
        return $this->event_type === 'exception';
    }
}
