<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Shipment extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tracking_number',
        'user_id',
        'company_id',
        'service_id',
        'quote_id',
        'status',
        'origin_address',
        'destination_address',
        'cargo_details',
        'total_weight',
        'weight_unit',
        'dimensions',
        'total_cost',
        'currency',
        'pickup_date',
        'estimated_delivery',
        'actual_delivery',
        'special_instructions',
        'documents',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected function casts(): array
    {
        return [
            'origin_address' => 'array',
            'destination_address' => 'array',
            'cargo_details' => 'array',
            'dimensions' => 'array',
            'documents' => 'array',
            'metadata' => 'array',
            'total_cost' => 'decimal:2',
            'total_weight' => 'decimal:2',
            'pickup_date' => 'datetime',
            'estimated_delivery' => 'datetime',
            'actual_delivery' => 'datetime',
        ];
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($shipment) {
            if (empty($shipment->tracking_number)) {
                $shipment->tracking_number = 'DCF' . strtoupper(Str::random(10));
            }
        });
    }

    /**
     * Get the user that owns the shipment.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the company that owns the shipment.
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * Get the service for the shipment.
     */
    public function service(): BelongsTo
    {
        return $this->belongsTo(Service::class);
    }

    /**
     * Get the quote that created this shipment.
     */
    public function quote(): BelongsTo
    {
        return $this->belongsTo(Quote::class);
    }

    /**
     * Get the tracking events for the shipment.
     */
    public function trackingEvents(): HasMany
    {
        return $this->hasMany(TrackingEvent::class)->orderBy('event_datetime');
    }

    /**
     * Get the latest tracking event.
     */
    public function latestTrackingEvent()
    {
        return $this->hasOne(TrackingEvent::class)->latestOfMany('event_datetime');
    }

    /**
     * Check if shipment is delivered.
     */
    public function isDelivered(): bool
    {
        return $this->status === 'delivered';
    }

    /**
     * Check if shipment is in transit.
     */
    public function isInTransit(): bool
    {
        return in_array($this->status, ['picked_up', 'in_transit', 'customs_clearance', 'out_for_delivery']);
    }

    /**
     * Scope a query to only include active shipments.
     */
    public function scopeActive($query)
    {
        return $query->whereNotIn('status', ['delivered', 'cancelled']);
    }

    /**
     * Add a tracking event to the shipment.
     */
    public function addTrackingEvent(string $eventType, string $description, array $location = null, string $source = 'system'): TrackingEvent
    {
        return $this->trackingEvents()->create([
            'event_type' => $eventType,
            'status' => $eventType,
            'description' => $description,
            'location' => $location,
            'event_datetime' => now(),
            'source' => $source,
        ]);
    }

    /**
     * Update shipment status and add tracking event.
     */
    public function updateStatus(string $status, string $description, array $location = null): void
    {
        $this->update(['status' => $status]);
        $this->addTrackingEvent($status, $description, $location);
    }
}
